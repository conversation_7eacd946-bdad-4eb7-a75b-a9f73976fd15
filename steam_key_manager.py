import tkinter as tk
from tkinter import ttk, messagebox, filedialog, Menu, font
import requests
import json
import os
import logging
import sys
import traceback
from datetime import datetime, timedelta, date
from tkcalendar import DateEntry

# 配置日志记录器
def setup_logger():
    """配置日志记录器，输出到控制台"""
    logger = logging.getLogger('SteamKeyManager')
    logger.setLevel(logging.DEBUG)  # 设置为DEBUG级别，输出所有日志
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器到记录器
    logger.addHandler(console_handler)
    
    return logger

# 创建全局日志记录器
logger = setup_logger()

class SteamKeyManager:
    def __init__(self, root):
        logger.info("初始化 SteamKeyManager 应用")
        self.root = root
        self.root.title("Steam激活码管理工具 - 高级版")
        self.root.geometry("1280x800")  # 增加窗口尺寸，提供更好的显示空间
        self.root.minsize(1000, 700)    # 设置最小窗口尺寸
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("steam_icon.ico")
            logger.debug("成功加载窗口图标")
        except Exception as e:
            logger.debug(f"无法加载窗口图标: {e}")
        
        # 设置全局字体和主题
        self.setup_appearance()
        
        # 配置文件路径
        self.config_file = "config.json"
        self.cookie = ""
        self.is_activated = False
        self.api_url = "https://run.ilovesteam.cn/apivy1zEkBayHSPeL1etBX8bJClu436z/get_key_info"
        self.total_count = 0
        self.current_data = []
        
        # 日期筛选变量
        self.start_date = None
        self.end_date = None
        self.enable_date_filter = False
        
        # 加载配置
        self.load_config()
        
        # 创建UI
        self.create_ui()
        
        # 创建右键菜单
        self.create_context_menu()
        
        logger.info("应用初始化完成")
        
    def setup_appearance(self):
        """设置应用程序外观"""
        # 设置字体
        default_font = font.nametofont("TkDefaultFont")
        default_font.configure(family="微软雅黑", size=10)
        self.root.option_add("*Font", default_font)
        
        # 配置全局样式
        style = ttk.Style()
        
        # 现代化配色方案 - 定义为类变量以便全局访问
        self.bg_color = "#f0f2f5"  # 更柔和的浅灰背景
        self.fg_color = "#1f2937"  # 更深的文字颜色，提高对比度
        self.accent_color = "#3b82f6"  # 现代蓝色强调色
        self.accent_hover = "#2563eb"  # 蓝色悬停色
        self.success_color = "#10b981"  # 现代绿色
        self.warning_color = "#f59e0b"  # 现代橙色
        self.danger_color = "#ef4444"   # 红色警告色
        self.border_color = "#e5e7eb"   # 边框颜色
        self.card_bg = "#ffffff"        # 卡片背景色
        self.header_bg = "#e5e7eb"      # 表头背景色
        self.selected_bg = "#dbeafe"    # 选中行的背景色
        
        # 配置基础样式
        style.configure("TFrame", background=self.bg_color)
        style.configure("TLabelFrame", background=self.bg_color, bordercolor=self.border_color)
        style.configure("TLabel", background=self.bg_color, foreground=self.fg_color)
        
        # 表格样式增强
        style.configure("Treeview", 
                       background=self.card_bg, 
                       foreground=self.fg_color, 
                       rowheight=32,  # 增加行高，提高可读性
                       fieldbackground=self.card_bg,
                       font=("微软雅黑", 10),
                       borderwidth=1,
                       relief="solid")
        style.configure("Treeview.Heading", 
                       font=("微软雅黑", 10, "bold"), 
                       background=self.header_bg, 
                       foreground=self.fg_color,
                       relief="flat",
                       borderwidth=1)
        
        # 添加表头悬停样式
        style.configure("Hover.Treeview.Heading",
                       font=("微软雅黑", 10, "bold"),
                       background="#d1d5db",  # 悬停时的背景色
                       foreground=self.fg_color,
                       relief="flat",
                       borderwidth=1)
                       
        style.map("Treeview",
                 background=[("selected", self.selected_bg)],
                 foreground=[("selected", self.fg_color)])
        
        # 输入框样式
        style.configure("TEntry", 
                       font=("微软雅黑", 10),
                       fieldbackground=self.card_bg,
                       bordercolor=self.border_color)
        
        # 标签框样式
        style.configure("TLabelframe.Label", 
                      font=("微软雅黑", 10, "bold"),
                      background=self.bg_color,
                      foreground=self.fg_color)
        
        # 复选框样式
        style.configure("TCheckbutton", 
                      background=self.bg_color,
                      foreground=self.fg_color)
        
        # 设置窗口背景色
        self.root.configure(background=self.bg_color)
        
    def create_button(self, parent, text, command, button_type="normal", width=None, height=None, icon=None):
        """创建统一样式的按钮
        
        参数:
            parent: 父级组件
            text: 按钮文本
            command: 按钮点击回调函数
            button_type: 按钮类型，可选值: "normal", "success", "warning", "danger", "secondary"
            width: 按钮宽度，可选
            height: 按钮高度，可选
            icon: 按钮图标，可选
        
        返回:
            创建的按钮对象
        """
        # 根据类型设置按钮颜色
        if button_type == "success":
            bg_color = self.success_color
            hover_color = "#059669"  # 深绿色
            text_color = "#ffffff"   # 白色文字
        elif button_type == "warning":
            bg_color = self.warning_color
            hover_color = "#d97706"  # 深橙色
            text_color = "#ffffff"   # 白色文字
        elif button_type == "danger":
            bg_color = self.danger_color
            hover_color = "#dc2626"  # 深红色
            text_color = "#ffffff"   # 白色文字
        elif button_type == "secondary":
            bg_color = "#e5e7eb"     # 浅灰色
            hover_color = "#d1d5db"  # 稍深的灰色
            text_color = "#1f2937"   # 深色文字
        else:  # normal
            bg_color = self.accent_color
            hover_color = self.accent_hover
            text_color = "#ffffff"   # 白色文字
        
        # 创建按钮
        button = tk.Button(
            parent,
            text=text,
            command=command,
            font=("微软雅黑", 10, "bold"),
            bg=bg_color,
            fg=text_color,
            activebackground=hover_color,
            activeforeground=text_color,
            relief=tk.FLAT,
            borderwidth=0,
            padx=12,
            pady=6,
            cursor="hand2",
            highlightthickness=0,  # 移除高亮边框
            highlightbackground=bg_color,  # 确保在Mac上没有边框
            overrelief=tk.RIDGE  # 点击时的效果
        )
        
        # 设置按钮尺寸（如果提供）
        if width:
            button.config(width=width)
        if height:
            button.config(height=height)
        
        # 添加鼠标悬停效果
        def on_enter(e):
            button['background'] = hover_color
            
        def on_leave(e):
            button['background'] = bg_color
        
        # 添加点击效果
        def on_press(e):
            button.config(relief=tk.SUNKEN)
            
        def on_release(e):
            button.config(relief=tk.FLAT)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        button.bind("<ButtonPress-1>", on_press)
        button.bind("<ButtonRelease-1>", on_release)
        
        return button
        
    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧控制面板和右侧数据显示的分割布局
        left_panel = ttk.Frame(main_frame, width=300)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_panel.pack_propagate(False)  # 防止子组件改变frame大小
        
        right_panel = ttk.Frame(main_frame)
        right_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # ===== 左侧控制面板 =====
        # 创建设置区域
        settings_frame = ttk.LabelFrame(left_panel, text="连接设置", padding="12")
        settings_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        # Cookie输入
        ttk.Label(settings_frame, text="Cookie:", font=("微软雅黑", 10, "bold")).pack(anchor=tk.W, padx=5, pady=(5, 2))
        self.cookie_entry = ttk.Entry(settings_frame, font=("微软雅黑", 10))
        self.cookie_entry.pack(fill=tk.X, padx=5, pady=(0, 5))
        self.cookie_entry.insert(0, self.cookie)
        
        # 保存配置按钮
        save_config_btn = self.create_button(settings_frame, text="保存配置", command=self.save_config, button_type="secondary")
        save_config_btn.pack(fill=tk.X, padx=5, pady=5)
        
        # 查询控制区域
        query_frame = ttk.LabelFrame(left_panel, text="查询控制", padding="12")
        query_frame.pack(fill=tk.X, pady=10, padx=5)
        
        # 激活状态选择
        status_frame = ttk.Frame(query_frame)
        status_frame.pack(fill=tk.X, pady=5)
        
        self.activated_var = tk.BooleanVar(value=self.is_activated)
        activated_check = ttk.Checkbutton(status_frame, text="查询已激活的激活码", variable=self.activated_var)
        activated_check.pack(anchor=tk.W)
        
        # 每页数量设置
        size_frame = ttk.Frame(query_frame)
        size_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(size_frame, text="每页数量:", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=(0, 5))
        self.size_spinbox = ttk.Spinbox(size_frame, from_=10, to=100, width=5, font=("微软雅黑", 10))
        self.size_spinbox.pack(side=tk.LEFT)
        self.size_spinbox.set(100)
        
        # 查询按钮
        query_all_btn = self.create_button(query_frame, text="查询全部数据", 
                                      command=self.fetch_all_data,
                                      button_type="success")
        query_all_btn.pack(fill=tk.X, pady=(10, 5))
        
        # 日期筛选区域
        date_filter_frame = ttk.LabelFrame(left_panel, text="日期筛选", padding="12")
        date_filter_frame.pack(fill=tk.X, pady=10, padx=5)
        
        # 启用日期筛选复选框
        self.date_filter_var = tk.BooleanVar(value=False)
        date_filter_check = ttk.Checkbutton(date_filter_frame, text="启用日期筛选", 
                                          variable=self.date_filter_var, 
                                          command=self.toggle_date_filter)
        date_filter_check.pack(anchor=tk.W, pady=(0, 5))
        
        # 日期选择器 - 开始日期
        date_start_frame = ttk.Frame(date_filter_frame)
        date_start_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(date_start_frame, text="开始日期:", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=(0, 5))
        today = datetime.now()
        self.start_date_entry = DateEntry(date_start_frame, width=12, 
                                        date_pattern='yyyy-mm-dd',
                                        state="readonly", 
                                        selectmode='day',
                                        showweeknumbers=False,
                                        font=("微软雅黑", 9),
                                        year=today.year, month=today.month, day=today.day)
        self.start_date_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.start_date_entry.configure(state="disabled")
        
        # 日期选择器 - 结束日期
        date_end_frame = ttk.Frame(date_filter_frame)
        date_end_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(date_end_frame, text="结束日期:", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=(0, 5))
        self.end_date_entry = DateEntry(date_end_frame, width=12, 
                                      date_pattern='yyyy-mm-dd',
                                      state="readonly", 
                                      selectmode='day',
                                      showweeknumbers=False,
                                      font=("微软雅黑", 9),
                                      year=today.year, month=today.month, day=today.day)
        self.end_date_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.end_date_entry.configure(state="disabled")
        
        # 日期筛选预设
        ttk.Label(date_filter_frame, text="快速选择:", font=("微软雅黑", 10, "bold")).pack(anchor=tk.W, pady=(10, 5))
        
        # 创建两行预设按钮
        preset_row1 = ttk.Frame(date_filter_frame)
        preset_row1.pack(fill=tk.X, pady=(0, 5))
        
        today_btn = self.create_button(preset_row1, text="今天", 
                                   command=lambda: self.set_date_preset("today"),
                                   button_type="secondary",
                                   width=8)
        today_btn.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        
        last7_btn = self.create_button(preset_row1, text="最近7天", 
                                   command=lambda: self.set_date_preset("last7"),
                                   button_type="secondary",
                                   width=8)
        last7_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        preset_row2 = ttk.Frame(date_filter_frame)
        preset_row2.pack(fill=tk.X, pady=(0, 5))
        
        last30_btn = self.create_button(preset_row2, text="最近30天", 
                                    command=lambda: self.set_date_preset("last30"),
                                    button_type="secondary",
                                    width=8)
        last30_btn.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        
        before_2024_btn = self.create_button(preset_row2, text="2024年底至今", 
                                        command=lambda: self.set_date_preset("before_2024"),
                                        button_type="secondary",
                                        width=8)
        before_2024_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        preset_row3 = ttk.Frame(date_filter_frame)
        preset_row3.pack(fill=tk.X)
        
        after_2024_btn = self.create_button(preset_row3, text="2025年前", 
                                        command=lambda: self.set_date_preset("after_2024"),
                                        button_type="secondary",
                                        width=8)
        after_2024_btn.pack(fill=tk.X)
        
        # ===== 右侧数据显示区域 =====
        # 顶部操作栏
        top_action_bar = ttk.Frame(right_panel)
        top_action_bar.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧统计信息
        self.stats_var = tk.StringVar(value="共 0 条记录")
        stats_label = ttk.Label(top_action_bar, textvariable=self.stats_var, font=("微软雅黑", 10, "bold"))
        stats_label.pack(side=tk.LEFT)
        
        # 右侧功能按钮
        buttons_frame = ttk.Frame(top_action_bar)
        buttons_frame.pack(side=tk.RIGHT)
        
        # 批量复制按钮
        copy_all_btn = self.create_button(buttons_frame, text="复制所有激活码", 
                                      command=self.copy_all_keys,
                                      button_type="normal")
        copy_all_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 复制选中按钮
        copy_selected_btn = self.create_button(buttons_frame, text="复制选中激活码", 
                                          command=self.copy_selected_keys,
                                          button_type="normal")
        copy_selected_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 特定格式复制按钮
        copy_format_btn = self.create_button(buttons_frame, text="特定格式复制", 
                                        command=self.copy_keys_formatted,
                                        button_type="success")
        copy_format_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 导出按钮
        export_btn = self.create_button(buttons_frame, text="导出数据", 
                                    command=self.export_data,
                                    button_type="secondary")
        export_btn.pack(side=tk.LEFT)
        
        # 数据表格区域
        data_frame = ttk.LabelFrame(right_panel, text="激活码列表", padding="12")
        data_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格容器框架
        table_container = ttk.Frame(data_frame)
        table_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格
        columns = ("激活码", "应用ID", "创建时间", "用户", "状态")
        self.tree = ttk.Treeview(table_container, columns=columns, show="headings", selectmode="extended")
        
        # 设置表头
        for col in columns:
            self.tree.heading(col, text=col, anchor=tk.CENTER)
        
        # 设置列宽和对齐方式
        self.tree.column("激活码", width=280, anchor=tk.W)  # 左对齐，增加宽度
        self.tree.column("应用ID", width=100, anchor=tk.CENTER)  # 居中对齐
        self.tree.column("创建时间", width=180, anchor=tk.CENTER)  # 居中对齐
        self.tree.column("用户", width=180, anchor=tk.W)  # 左对齐
        self.tree.column("状态", width=100, anchor=tk.CENTER)  # 居中对齐
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(table_container, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_container, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        # 配置表格的滚动条
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 放置表格和滚动条 - 使用网格布局以确保滚动条正确对齐
        self.tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        scrollbar_x.grid(row=1, column=0, sticky="ew")
        
        # 配置网格权重，使表格可以扩展
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)
        
        # 添加双击事件
        self.tree.bind("<Double-1>", self.on_double_click)
        # 添加右键点击事件
        self.tree.bind("<Button-3>", self.show_context_menu)
        
        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.GROOVE, anchor=tk.W, padding=(10, 5))
        status_bar.pack(fill=tk.X)
        
        # 添加视觉反馈和交互效果
        self.add_hover_effects()
    
    def add_hover_effects(self):
        """为各种控件添加鼠标悬停效果，提升用户体验"""
        # 为表格添加行悬停效果
        def on_tree_hover(event):
            item = self.tree.identify_row(event.y)
            if item:
                self.tree.tk.call(self.tree, "tag", "remove", "hover")
                self.tree.tk.call(self.tree, "tag", "add", "hover", item)
                
        def on_tree_leave(event):
            self.tree.tk.call(self.tree, "tag", "remove", "hover")
            
        # 配置悬停标签样式
        self.tree.tag_configure("hover", background="#e1effe")  # 浅蓝色悬停效果
        
        # 绑定鼠标移动和离开事件
        self.tree.bind("<Motion>", on_tree_hover)
        self.tree.bind("<Leave>", on_tree_leave)
        
        # 移除表头悬停效果，因为不支持background选项
        
        # 为状态栏添加动态效果
        def update_status_bar():
            """更新状态栏样式，创建呼吸效果"""
            if hasattr(self, 'status_pulse') and self.status_pulse:
                current_bg = status_bar.cget('background')
                if current_bg == self.bg_color:
                    status_bar.configure(background="#e5e7eb")
                else:
                    status_bar.configure(background=self.bg_color)
                self.root.after(2000, update_status_bar)  # 每2秒更新一次
                
        # 设置状态栏动态效果标志
        self.status_pulse = False
    
    def toggle_date_filter(self):
        """切换日期筛选状态"""
        if self.date_filter_var.get():
            self.start_date_entry.configure(state="readonly")
            self.end_date_entry.configure(state="readonly")
            self.enable_date_filter = True
            self.update_date_filter()
        else:
            self.start_date_entry.configure(state="disabled")
            self.end_date_entry.configure(state="disabled")
            self.enable_date_filter = False
    
    def set_date_preset(self, preset):
        """设置日期筛选预设"""
        today = datetime.now()
        
        if preset == "today":
            self.start_date_entry.set_date(today)
            self.end_date_entry.set_date(today)
        elif preset == "last7":
            self.start_date_entry.set_date(today - timedelta(days=7))
            self.end_date_entry.set_date(today)
        elif preset == "last30":
            self.start_date_entry.set_date(today - timedelta(days=30))
            self.end_date_entry.set_date(today)
        elif preset == "before_2024":
            # 2024年最后一天到现在
            dec_31_2024 = datetime(2024, 12, 31)
            self.start_date_entry.set_date(dec_31_2024)
            self.end_date_entry.set_date(today)
            logger.debug(f"设置日期范围: 从 {dec_31_2024.strftime('%Y-%m-%d')} 到 {today.strftime('%Y-%m-%d')}")
        elif preset == "after_2024":
            # 2025年第一天之前 (即2024年及之前)
            jan_1_2025 = datetime(2025, 1, 1)
            self.start_date_entry.set_date(datetime(2000, 1, 1))  # 设置一个较早的日期作为开始
            self.end_date_entry.set_date(jan_1_2025 - timedelta(days=1))  # 2024年12月31日
            logger.debug(f"设置日期范围: 从 2000-01-01 到 {(jan_1_2025 - timedelta(days=1)).strftime('%Y-%m-%d')}")
        
        # 如果日期筛选未启用，自动启用
        if not self.date_filter_var.get():
            self.date_filter_var.set(True)
            self.toggle_date_filter()
        else:
            # 如果日期筛选已启用，则更新筛选
            self.update_date_filter()
    
    def update_date_filter(self):
        """更新日期筛选"""
        if self.enable_date_filter:
            # 获取日期选择器的日期（date对象）
            start_date = self.start_date_entry.get_date()
            end_date = self.end_date_entry.get_date()
            
            # 将start_date转换为datetime，设置为当天的开始时刻 (00:00:00)
            self.start_date = datetime.combine(start_date, datetime.min.time())
            
            # 将end_date转换为datetime，设置为当天的最后一刻 (23:59:59.999999)
            self.end_date = datetime.combine(end_date, datetime.max.time())
            
            logger.debug(f"日期筛选范围: 从 {self.start_date} 到 {self.end_date}")
            self.update_filtered_data()
        else:
            self.start_date = None
            self.end_date = None
            # 重新显示所有数据
            self.display_data({'key_list': self.current_data, 'count': self.total_count})
    
    def update_filtered_data(self):
        """根据日期筛选更新数据显示"""
        if self.start_date and self.end_date and self.current_data:
            filtered_data = []
            for item in self.current_data:
                create_time_unix = item.get('create_time', 0)
                create_time = datetime.fromtimestamp(create_time_unix)
                
                # 确保start_date是datetime对象，而不是date对象
                if isinstance(self.start_date, date) and not isinstance(self.start_date, datetime):
                    start_datetime = datetime.combine(self.start_date, datetime.min.time())
                else:
                    start_datetime = self.start_date
                
                # 确保end_date是datetime对象，而不是date对象
                if isinstance(self.end_date, date) and not isinstance(self.end_date, datetime):
                    end_datetime = datetime.combine(self.end_date, datetime.max.time())
                else:
                    end_datetime = self.end_date
                
                if start_datetime <= create_time <= end_datetime:
                    filtered_data.append(item)
            
            # 更新显示
            self.display_data({'key_list': filtered_data, 'count': len(filtered_data)})
            self.stats_var.set(f"共 {self.total_count} 条记录，筛选后 {len(filtered_data)} 条")
        else:
            self.display_data({'key_list': self.current_data, 'count': self.total_count})
    
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="复制激活码", command=lambda: self.copy_selected("激活码"))
        self.context_menu.add_command(label="复制应用ID", command=lambda: self.copy_selected("应用ID"))
        self.context_menu.add_command(label="复制行信息", command=self.copy_row)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="查看详情", command=self.show_details)
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        # 先确认有选中项
        if self.tree.selection():
            self.context_menu.tk_popup(event.x_root, event.y_root)
    
    def copy_all_keys(self):
        """复制所有激活码，使用逗号分隔"""
        items = self.tree.get_children()
        if not items:
            messagebox.showinfo("提示", "没有数据可复制")
            return
            
        keys = []
        for item_id in items:
            values = self.tree.item(item_id, "values")
            keys.append(values[0])  # 激活码在第一列
            
        # 用逗号连接所有激活码
        text = ",".join(keys)
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.status_var.set(f"已复制 {len(keys)} 个激活码到剪贴板")
    
    def copy_selected_keys(self):
        """复制选中的激活码，使用逗号分隔"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showinfo("提示", "未选中任何数据")
            return
            
        keys = []
        for item_id in selected_items:
            values = self.tree.item(item_id, "values")
            keys.append(values[0])  # 激活码在第一列
            
        # 用逗号连接所有激活码
        text = ",".join(keys)
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.status_var.set(f"已复制 {len(keys)} 个选中的激活码到剪贴板")
    
    def copy_keys_formatted(self):
        """以特定格式复制激活码（所有激活码连成一行，使用英文逗号连接）"""
        items = self.tree.selection() if self.tree.selection() else self.tree.get_children()
        
        if not items:
            messagebox.showinfo("提示", "没有数据可复制")
            return
            
        keys = []
        for item_id in items:
            values = self.tree.item(item_id, "values")
            keys.append(values[0])  # 激活码在第一列
            
        # 所有激活码连成一行，使用英文逗号连接
        text = ",".join(keys)
        
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        
        # 显示成功信息
        count = len(keys)
        if self.tree.selection():
            self.status_var.set(f"已以特定格式复制 {count} 个选中的激活码到剪贴板")
        else:
            self.status_var.set(f"已以特定格式复制所有 {count} 个激活码到剪贴板")
    
    def copy_selected(self, column_name):
        """复制选中项的特定列"""
        selected_item = self.tree.selection()
        if not selected_item:
            return
            
        item_values = self.tree.item(selected_item[0], "values")
        
        # 获取列索引
        columns = ("激活码", "应用ID", "创建时间", "用户", "状态")
        if column_name in columns:
            index = columns.index(column_name)
            value = item_values[index]
            self.root.clipboard_clear()
            self.root.clipboard_append(value)
            self.status_var.set(f"{column_name} 已复制到剪贴板: {value}")
    
    def copy_row(self):
        """复制整行信息"""
        selected_item = self.tree.selection()
        if not selected_item:
            return
            
        item_values = self.tree.item(selected_item[0], "values")
        text = "\t".join([str(v) for v in item_values])
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.status_var.set("行信息已复制到剪贴板")
    
    def show_details(self):
        """显示详细信息"""
        selected_item = self.tree.selection()
        if not selected_item:
            return
            
        item_values = self.tree.item(selected_item[0], "values")
        key = item_values[0]
        app_id = item_values[1]
        create_time = item_values[2]
        user = item_values[3]
        status = item_values[4]
        
        # 查找对应的完整数据
        key_info = None
        for info in self.current_data:
            if info.get('key', '') == key:
                key_info = info
                break
        
        # 创建详情窗口
        details_window = tk.Toplevel(self.root)
        details_window.title("激活码详情")
        details_window.geometry("500x380")  # 略微增加尺寸
        details_window.resizable(True, True)
        details_window.configure(background="#f5f5f7")  # 使用与主窗口相同的背景色
        
        # 设置窗口图标（如果有的话）
        try:
            details_window.iconbitmap("steam_icon.ico")
        except:
            pass  # 如果图标不存在，忽略错误
        
        # 详情内容
        frame = ttk.Frame(details_window, padding="15")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格式布局
        info_frame = ttk.Frame(frame)
        info_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        row = 0
        # 激活码信息 - 使用更美观的布局
        ttk.Label(info_frame, text="激活码:", font=("微软雅黑", 10, "bold")).grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        key_value = ttk.Label(info_frame, text=key, font=("微软雅黑", 10))
        key_value.grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        
        ttk.Label(info_frame, text="应用ID:", font=("微软雅黑", 10, "bold")).grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(info_frame, text=app_id, font=("微软雅黑", 10)).grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        
        ttk.Label(info_frame, text="创建时间:", font=("微软雅黑", 10, "bold")).grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(info_frame, text=create_time, font=("微软雅黑", 10)).grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        
        ttk.Label(info_frame, text="用户:", font=("微软雅黑", 10, "bold")).grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(info_frame, text=user, font=("微软雅黑", 10)).grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        
        ttk.Label(info_frame, text="状态:", font=("微软雅黑", 10, "bold")).grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        status_label = ttk.Label(info_frame, text=status, font=("微软雅黑", 10))
        status_label.grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        if status == "已激活":
            status_label.configure(foreground="#4CAF50")
        else:
            status_label.configure(foreground="#F44336")
        row += 1
        
        if key_info:
            # 显示DLC列表
            ttk.Label(info_frame, text="DLC列表:", font=("微软雅黑", 10, "bold")).grid(row=row, column=0, sticky=tk.NW, padx=5, pady=5)
            dlc_list = key_info.get('dlc_list', [])
            
            if dlc_list:
                dlc_text = "\n".join([str(dlc) for dlc in dlc_list])
            else:
                dlc_text = "无"
            
            dlc_label = ttk.Label(info_frame, text=dlc_text, font=("微软雅黑", 10))
            dlc_label.grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
            row += 1
            
            # 其他属性
            ttk.Label(info_frame, text="属性值:", font=("微软雅黑", 10, "bold")).grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
            attribute = key_info.get('attribute', 'N/A')
            ttk.Label(info_frame, text=str(attribute), font=("微软雅黑", 10)).grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
            row += 1
        
        # 按钮区域
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=15)
        
        # 复制按钮
        copy_btn = self.create_button(button_frame, text="复制激活码", 
                                  command=lambda: self.copy_to_clipboard(key),
                                  button_type="success")
        copy_btn.pack(side=tk.LEFT, padx=8, pady=8)
        
        close_btn = self.create_button(button_frame, text="关闭", 
                                   command=details_window.destroy)
        close_btn.pack(side=tk.RIGHT, padx=8, pady=8)
    
    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.status_var.set(f"已复制到剪贴板: {text}")
    
    def on_double_click(self, event):
        """双击表格行时的动作"""
        self.show_details()
    
    def clean_cookie(self, cookie_value):
        """清理Cookie值，移除换行符、前导和尾随空白字符"""
        if not cookie_value:
            return ""
            
        # 记录原始长度，用于调试
        original_length = len(cookie_value)
        
        try:
            # 移除所有换行符和回车符
            cookie_value = cookie_value.replace('\n', '').replace('\r', '')
            
            # 移除前导和尾随空白字符
            cookie_value = cookie_value.strip()
            
            # 分割Cookie，确保每个部分都是有效的
            parts = [part.strip() for part in cookie_value.split(';')]
            cookie_value = '; '.join(filter(None, parts))  # 使用分号+空格重新连接，并过滤空字符串
            
            # 记录清理后的长度，用于调试
            cleaned_length = len(cookie_value)
            if original_length != cleaned_length:
                logger.debug(f"Cookie已清理: 原始长度={original_length}, 清理后长度={cleaned_length}")
                
            return cookie_value
        except Exception as e:
            logger.warning(f"清理Cookie时出错: {e}")
            # 出错时返回原始值，让后续代码处理可能的错误
            return cookie_value
    
    def load_config(self):
        """加载配置文件"""
        logger.info(f"尝试加载配置文件: {self.config_file}")
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.cookie = config.get('cookie', '')
                    self.is_activated = config.get('is_activated', False)
                    logger.debug(f"配置加载成功: 激活状态={self.is_activated}")
                    self.status_var.set("配置已加载") if hasattr(self, 'status_var') else None
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                logger.debug(traceback.format_exc())
        else:
            logger.warning(f"配置文件不存在: {self.config_file}")
    
    def save_config(self):
        """保存配置文件"""
        logger.info("保存配置文件")
        self.cookie = self.cookie_entry.get()
        try:
            # 日志中不显示完整cookie
            masked_cookie = self.cookie[:10] + '...' if len(self.cookie) > 10 else self.cookie
            config = {
                'cookie': masked_cookie,
                'is_activated': self.activated_var.get()
            }
            logger.debug(f"保存配置: {config}")
            
            # 实际保存时使用完整cookie
            save_config = {
                'cookie': self.cookie,
                'is_activated': self.activated_var.get()
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(save_config, f, indent=4, ensure_ascii=False)
            
            self.status_var.set("配置已保存")
            messagebox.showinfo("成功", "配置已保存")
            logger.info("配置保存成功")
        except Exception as e:
            error_msg = f"保存配置失败: {e}"
            logger.error(error_msg)
            logger.debug(traceback.format_exc())
            messagebox.showerror("错误", error_msg)
    
    def fetch_data(self):
        """获取激活码数据 - 已重定向到fetch_all_data方法"""
        logger.info("fetch_data方法已被调用，重定向到fetch_all_data方法")
        # 分页功能已移除，重定向到fetch_all_data方法
        self.fetch_all_data()
    
    def fetch_all_data(self):
        """获取所有页面的激活码数据"""
        logger.info("开始获取所有页面的激活码数据")
        raw_cookie = self.cookie_entry.get()
        self.cookie = self.clean_cookie(raw_cookie)
        self.is_activated = self.activated_var.get()
        
        if not self.cookie:
            error_msg = "请先填写Cookie"
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
            return
        
        # 设置状态栏动态效果
        self.status_pulse = True
        self.status_var.set("正在获取所有数据...")
        
        # 禁用查询按钮，避免重复点击
        for widget in self.root.winfo_children():
            if isinstance(widget, tk.Button) and widget['text'] in ["查询全部数据", "查询当前页"]:
                widget.configure(state=tk.DISABLED)
        self.root.update()
        
        try:
            # 检查Cookie格式
            if '\n' in self.cookie or '\r' in self.cookie:
                logger.warning("Cookie中仍包含换行符，尝试再次清理")
                self.cookie = self.clean_cookie(self.cookie)
            
            # 实际请求使用完整cookie
            request_headers = {
                'Cookie': self.cookie,
                'Content-Type': 'application/json'
            }
            
            # 首先获取第一页，了解总记录数
            page = 1
            page_size = int(self.size_spinbox.get())
            all_data = []
            
            logger.info(f"获取第 {page} 页数据，每页 {page_size} 条")
            
            payload = {
                'page': page,
                'size': page_size,
                'activated': self.is_activated
            }
            
            response = requests.post(self.api_url, headers=request_headers, json=payload)
            
            if response.status_code != 200:
                error_msg = f"请求失败，状态码: {response.status_code}"
                logger.error(error_msg)
                logger.debug(f"响应内容: {response.text}")
                messagebox.showerror("错误", error_msg)
                self.status_var.set(error_msg)
                return
            
            data = response.json()
            if data.get('code') != 0:
                error_msg = data.get('message', '未知错误')
                logger.error(f"API返回错误: {error_msg}")
                messagebox.showerror("错误", f"API返回错误: {error_msg}")
                self.status_var.set(f"获取数据失败: {error_msg}")
                return
            
            result_data = data.get('data', {})
            self.total_count = result_data.get('count', 0)
            page_data = result_data.get('key_list', [])
            all_data.extend(page_data)
            
            logger.info(f"成功获取第 {page} 页数据: {len(page_data)} 条，总记录数: {self.total_count}")
            
            # 计算总页数
            total_pages = (self.total_count + page_size - 1) // page_size
            
            # 获取剩余页面的数据
            for page in range(2, total_pages + 1):
                self.status_var.set(f"正在获取第 {page}/{total_pages} 页数据...")
                self.root.update()
                
                logger.info(f"获取第 {page}/{total_pages} 页数据")
                
                payload = {
                    'page': page,
                    'size': page_size,
                    'activated': self.is_activated
                }
                
                response = requests.post(self.api_url, headers=request_headers, json=payload)
                
                if response.status_code != 200:
                    logger.warning(f"获取第 {page} 页失败，状态码: {response.status_code}")
                    continue
                
                data = response.json()
                if data.get('code') != 0:
                    logger.warning(f"获取第 {page} 页失败，API错误: {data.get('message', '未知错误')}")
                    continue
                
                page_data = data.get('data', {}).get('key_list', [])
                all_data.extend(page_data)
                logger.info(f"成功获取第 {page} 页数据: {len(page_data)} 条")
            
            # 保存所有数据
            self.current_data = all_data
            logger.info(f"成功获取所有数据，共 {len(all_data)} 条")
            
            # 如果启用了日期筛选，则应用日期筛选
            if self.enable_date_filter:
                logger.debug("应用日期筛选")
                self.update_date_filter()
            else:
                # 显示所有数据
                result_data['key_list'] = all_data
                self.display_data(result_data)
                self.stats_var.set(f"共 {self.total_count} 条记录，显示全部 {len(all_data)} 条")
                self.status_var.set(f"成功获取所有数据，共 {len(all_data)} 条")
                
                # 重新启用查询按钮
                for widget in self.root.winfo_children():
                    if isinstance(widget, tk.Button) and widget['text'] in ["查询全部数据", "查询当前页"]:
                        widget.configure(state=tk.NORMAL)
                
                # 关闭状态栏动态效果
                self.status_pulse = False
                
        except requests.exceptions.InvalidHeader as e:
            error_msg = f"Cookie格式无效: {e}"
            logger.error(error_msg)
            logger.debug(traceback.format_exc())
            messagebox.showerror("错误", "Cookie格式无效，请检查并删除所有换行符和特殊字符。\n\n详细错误: " + str(e))
            self.status_var.set("Cookie格式无效，请检查")
            
            # 重新启用查询按钮
            for widget in self.root.winfo_children():
                if isinstance(widget, tk.Button) and widget['text'] in ["查询全部数据", "查询当前页"]:
                    widget.configure(state=tk.NORMAL)
            
            # 关闭状态栏动态效果
            self.status_pulse = False
        except Exception as e:
            error_msg = f"获取所有数据失败: {e}"
            logger.error(error_msg)
            logger.debug(traceback.format_exc())
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
            
            # 重新启用查询按钮
            for widget in self.root.winfo_children():
                if isinstance(widget, tk.Button) and widget['text'] in ["查询全部数据", "查询当前页"]:
                    widget.configure(state=tk.NORMAL)
            
            # 关闭状态栏动态效果
            self.status_pulse = False
    
    def display_data(self, data):
        """显示数据"""
        logger.info("开始显示数据")
        
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        logger.debug("已清空表格中的现有数据")
        
        # 插入新数据
        key_list = data.get('key_list', [])
        logger.info(f"准备显示 {len(key_list)} 条数据")
        
        try:
            for index, key_info in enumerate(key_list):
                key = key_info.get('key', '')
                app_id = key_info.get('app_id', '')
                create_time_unix = key_info.get('create_time', 0)
                create_time = datetime.fromtimestamp(create_time_unix).strftime('%Y-%m-%d %H:%M:%S')
                user = key_info.get('user', '')
                
                # 根据状态设置不同的标签
                if self.is_activated:
                    status = "已激活"
                    tag = 'activated'
                else:
                    status = "未激活"
                    tag = 'not_activated'
                
                # 插入数据并应用标签
                item_id = self.tree.insert('', tk.END, values=(key, app_id, create_time, user, status))
                
                # 应用行标签（奇偶行 + 状态）
                if index % 2 == 0:
                    self.tree.item(item_id, tags=('even', tag))
                else:
                    self.tree.item(item_id, tags=('odd', tag))
                
                # 每插入100条数据更新一次UI，避免界面卡顿
                if index % 100 == 0 and index > 0:
                    self.root.update()
                    logger.debug(f"已插入 {index} 条数据")
            
            logger.info(f"所有数据插入完成，共 {len(key_list)} 条")
            
            # 配置标签样式
            self.tree.tag_configure('even', background='#f8fafc')
            self.tree.tag_configure('odd', background=self.card_bg)
            self.tree.tag_configure('activated', foreground='#059669')  # 已激活的显示为绿色
            self.tree.tag_configure('not_activated', foreground=self.fg_color)  # 未激活的显示为正常颜色
            logger.debug("已设置行样式")
        
        except Exception as e:
            error_msg = f"显示数据时出错: {e}"
            logger.error(error_msg)
            logger.debug(traceback.format_exc())
            messagebox.showerror("错误", error_msg)
    
    def export_data(self):
        """导出数据"""
        # 检查是否有数据
        if not self.tree.get_children():
            messagebox.showerror("错误", "没有数据可导出")
            return
            
        # 获取保存文件路径
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("CSV文件", "*.csv"), ("所有文件", "*.*")],
            title="保存数据"
        )
        
        if not file_path:
            return
            
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # 写入表头
                columns = ["激活码", "应用ID", "创建时间", "用户", "状态"]
                f.write("\t".join(columns) + "\n")
                
                # 写入数据
                for item_id in self.tree.get_children():
                    values = self.tree.item(item_id, 'values')
                    f.write("\t".join(str(v) for v in values) + "\n")
                
            messagebox.showinfo("成功", f"数据已成功导出到: {file_path}")
            self.status_var.set(f"数据已成功导出到: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"导出数据失败: {e}")
            self.status_var.set(f"导出数据失败: {e}")

def main():
    root = tk.Tk()
    app = SteamKeyManager(root)
    
    # 应用启动后自动查询所有数据
    root.after(1000, app.fetch_all_data)  # 延迟1秒后执行，确保UI已完全加载
    
    root.mainloop()

if __name__ == "__main__":
    main() 